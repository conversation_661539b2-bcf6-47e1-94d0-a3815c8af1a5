'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON><PERSON>, Card, CardHeader, CardBody, CardFooter, Link, Input } from '@heroui/react';
import clsx from 'clsx';
import { useState, useEffect, useCallback } from 'react';


export default function Hero({ locale = 'en', color = 'black' }) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showFullscreenTip, setShowFullscreenTip] = useState(false);
  const [customColor, setCustomColor] = useState('#000000');

  // 处理颜色输入变化
  const handleColorInputChange = (value) => {
    setCustomColor(value);
  };

  const t = function (key) {
    return getTranslation(locale, key);
  }

  const colorDatas = {
    black: {
      title: t('Black Screen Tool'),
      colorValue: "#000000",
      label: t('Black'),
      labelTextColor: "text-white",
    },
    white: {
      title: t('White Screen Tool'),
      colorValue: "#ffffff",
      label: t('White'),
      labelTextColor: "text-black",
    },
    red: {
      title: t('Red Screen Tool'),
      colorValue: "#ff0000",
      label: t('Red'),
      labelTextColor: "text-white",
    },
    green: {
      title: t('Green Screen Tool'),
      colorValue: "#00ff00",
      label: t('Green'),
      labelTextColor: "text-white",
    },
    blue: {
      title: t('Blue Screen Tool'),
      colorValue: "#0000ff",
      label: t('Blue'),
      labelTextColor: "text-white",
    },
    yellow: {
      title: t('Yellow Screen Tool'),
      colorValue: "#ffff00",
      label: t('Yellow'),
      labelTextColor: "text-black",
    },
    orange: {
      title: t('Orange Screen Tool'),
      colorValue: "#FFA500",
      label: t('Orange'),
      labelTextColor: "text-white",
    },
    pink: {
      title: t('Pink Screen Tool'),
      colorValue: "#FF69B4",
      label: t('Pink'),
      labelTextColor: "text-white",
    },
    purple: {
      title: t('Purple Screen Tool'),
      colorValue: "#800080",
      label: t('Purple'),
      labelTextColor: "text-white",
    },
  };

  const { title, colorValue: defaultColorValue } = colorDatas[color];
  const colorValue = color === 'custom' ? customColor : defaultColorValue;

  // 进入全屏模式
  const enterFullscreen = useCallback(() => {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen();
    } else if (document.documentElement.msRequestFullscreen) {
      document.documentElement.msRequestFullscreen();
    }
    setIsFullscreen(true);
    setShowFullscreenTip(true);

    // 3秒后隐藏提示
    setTimeout(() => {
      setShowFullscreenTip(false);
    }, 3000);
  }, []);

  // 退出全屏模式
  const exitFullscreen = useCallback(() => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    setIsFullscreen(false);
  }, []);

  // 切换全屏模式
  const toggleFullscreen = useCallback(() => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'f':
        case 'F':
          event.preventDefault();
          if (!isFullscreen) {
            enterFullscreen();
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (isFullscreen) {
            exitFullscreen();
          }
          break;
        case ' ':
          event.preventDefault();
          toggleFullscreen();
          break;
        default:
          break;
      }
    };

    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, [isFullscreen, enterFullscreen, exitFullscreen, toggleFullscreen]);

  // 全屏时的渲染
  if (isFullscreen) {
    return (
      <div
        className={clsx(
          'fixed inset-0 z-50 cursor-pointer flex items-center justify-center',
          `bg-[${colorValue}]`
        )}
        onClick={toggleFullscreen}
      >
        {/* 3秒提示 */}
        {showFullscreenTip && (
          <div className="text-white/80 text-lg text-center">
            {t('Press F for fullscreen, ESC to exit, SPACE to toggle')}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {title}
      </h1>
      <div className="grid lg:grid-cols-2 gap-8 my-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div
              className={clsx(
                'aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group',
                `bg-[${colorValue}]`
              )}
              onClick={enterFullscreen}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {/* 1. 背景色使用 bgColor， 2. 在右上角显示colorValue， 类似tag的效果，背景透明， 3. 当鼠标进入时，在中间显示“click to enter full screen”， 类似tag的效果 4. 点击进入全屏 5, 增加快捷键操作，F 进入全屏， ESC 退出全屏，回到当前页面，Space或者鼠标点击，Toggle*/}
              {/* 颜色值显示在右上角 */}
              <div className="absolute top-2 right-2 text-xs text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-black/80 px-2 py-1 rounded">
                {colorValue}
              </div>

              {/* 鼠标悬停时显示提示 */}
              {isHovered && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <div className="text-white/70 text-sm">
                    {t('Click to enter full screen')}
                  </div>
                </div>
              )}
            </div>
          </CardBody>
          <CardFooter className='flex justify-center items-center'>
            <p className='text-sm text-gray-500 mb-2'>{t('Press F for fullscreen, ESC to exit, SPACE to toggle')}</p>
          </CardFooter>
        </Card>
        <div className='w-full'>
          <Card>
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Color Selection')}</h3>
            </CardHeader>
            <CardBody>
              <div className='flex items-center gap-4 flex-wrap'>
                {Object.keys(colorDatas).map((colorKey) => (
                  <Button
                    key={colorKey}
                    href={`/${colorKey}-screen`}
                    as={Link}
                    className={clsx('border-2 border-gray-300 min-w-[90px]', colorDatas[colorKey].labelTextColor)}
                    style={{
                      backgroundColor: colorDatas[colorKey].colorValue
                    }}
                  >
                    {colorDatas[colorKey].label}
                  </Button>
                ))}
                <Input
                  value={customColor}
                  onChange={(e) => handleColorInputChange(e.target.value)}
                  placeholder="#000000"
                  className="w-32"
                  size="sm"
                  label={t('Color Value')}
                />
              </div>
            </CardBody>
          </Card>
          <Card className="mt-6">
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Download Image')}</h3>
            </CardHeader>
            <CardBody className='px-6'>
            </CardBody>
          </Card>
        </div>
      </div>
      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Timer Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('OLED Protection Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
      </div>
    </div >
  );
}
