'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardHeader, CardBody, CardFooter } from '@heroui/react';
import clsx from 'clsx';

export default function Hero({ locale = 'en', color = 'black' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const data = {
    black: {
      title: t('Black Screen Tool'),
      colorValue: "#000000",
      bgColor: "bg-black",
    },
    white: {
      title: t('White Screen Tool'),
      colorValue: "#ffffff",
      bgColor: "bg-white",
    },
    red: {
      title: t('Red Screen Tool'),
      colorValue: "#ff0000",
      bgColor: "bg-red",
    },
    green: {
      title: t('Green Screen Tool'),
      colorValue: "#00ff00",
      bgColor: "bg-green",
    },
    blue: {
      title: t('Blue Screen Tool'),
      colorValue: "#0000ff",
      bgColor: "bg-blue",
    },
    yellow: {
      title: t('Yellow Screen Tool'),
      colorValue: "#ffff00",
      bgColor: "bg-yellow",
    },
    orange: {
      title: t('Orange Screen Tool'),
      colorValue: "#FFA500",
      bgColor: "bg-orange",
    },
    pink: {
      title: t('Pink Screen Tool'),
      colorValue: "#FF69B4",
      bgColor: "bg-pink",
    },
    purple: {
      title: t('Purple Screen Tool'),
      colorValue: "#800080",
      bgColor: "bg-purple",
    },
  };

  const { title, colorValue, bgColor } = data[color];

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {title}
      </h1>
      <div className="grid lg:grid-cols-2 gap-8 my-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className={clsx()} 'aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'>
              {/*  */}
            </div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <div className='w-full'>
          <Card>
            <CardBody>
              <CardHeader className='px-6'>
                <h3 className="text-lg font-semibold">{t('Color Selection')}</h3>
              </CardHeader>
            </CardBody>
          </Card>
          <Card className="mt-6">
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Download Image')}</h3>
            </CardHeader>
            <CardBody className='px-6'>
            </CardBody>
          </Card>
        </div>
      </div>
      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Timer Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('OLED Protection Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
      </div>
    </div >
  );
}
