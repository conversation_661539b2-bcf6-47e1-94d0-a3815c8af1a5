'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardHeader, CardBody, CardFooter, Chip } from '@heroui/react';
import clsx from 'clsx';
import { useState, useEffect, useCallback } from 'react';

export default function Hero({ locale = 'en', color = 'black' }) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const router = useRouter();

  const t = function (key) {
    return getTranslation(locale, key);
  }

  const data = {
    black: {
      title: t('Black Screen Tool'),
      colorValue: "#000000",
      bgColor: "bg-black",
    },
    white: {
      title: t('White Screen Tool'),
      colorValue: "#ffffff",
      bgColor: "bg-white",
    },
    red: {
      title: t('Red Screen Tool'),
      colorValue: "#ff0000",
      bgColor: "bg-red",
    },
    green: {
      title: t('Green Screen Tool'),
      colorValue: "#00ff00",
      bgColor: "bg-green",
    },
    blue: {
      title: t('Blue Screen Tool'),
      colorValue: "#0000ff",
      bgColor: "bg-blue",
    },
    yellow: {
      title: t('Yellow Screen Tool'),
      colorValue: "#ffff00",
      bgColor: "bg-yellow",
    },
    orange: {
      title: t('Orange Screen Tool'),
      colorValue: "#FFA500",
      bgColor: "bg-orange",
    },
    pink: {
      title: t('Pink Screen Tool'),
      colorValue: "#FF69B4",
      bgColor: "bg-pink",
    },
    purple: {
      title: t('Purple Screen Tool'),
      colorValue: "#800080",
      bgColor: "bg-purple",
    },
  };

  const { title, colorValue, bgColor } = data[color];

  // 进入全屏模式
  const enterFullscreen = useCallback(() => {
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen();
    } else if (document.documentElement.webkitRequestFullscreen) {
      document.documentElement.webkitRequestFullscreen();
    } else if (document.documentElement.msRequestFullscreen) {
      document.documentElement.msRequestFullscreen();
    }
    setIsFullscreen(true);
  }, []);

  // 退出全屏模式
  const exitFullscreen = useCallback(() => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    setIsFullscreen(false);
  }, []);

  // 切换全屏模式
  const toggleFullscreen = useCallback(() => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'f':
        case 'F':
          event.preventDefault();
          if (!isFullscreen) {
            enterFullscreen();
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (isFullscreen) {
            exitFullscreen();
          }
          break;
        case ' ':
          event.preventDefault();
          toggleFullscreen();
          break;
        default:
          break;
      }
    };

    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, [isFullscreen, enterFullscreen, exitFullscreen, toggleFullscreen]);

  // 全屏时的渲染
  if (isFullscreen) {
    return (
      <div
        className={clsx(
          'fixed inset-0 z-50 cursor-pointer flex items-center justify-center',
          bgColor
        )}
        onClick={toggleFullscreen}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* 颜色值显示在右上角 */}
        <Chip
          variant="bordered"
          className="absolute top-4 right-4 bg-transparent border-white/50 text-white"
        >
          {colorValue}
        </Chip>

        {/* 鼠标悬停时显示提示 */}
        {isHovered && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Chip
              variant="bordered"
              className="bg-transparent border-white/50 text-white text-lg px-6 py-2"
            >
              {t('Press F for fullscreen, ESC to exit, SPACE to toggle')}
            </Chip>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="text-center pt-10 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {title}
      </h1>
      <div className="grid lg:grid-cols-2 gap-8 my-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Screen Preview')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div
              className={clsx(
                'aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group',
                bgColor
              )}
              onClick={enterFullscreen}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {/* 1. 背景色使用 bgColor， 2. 在右上角显示colorValue， 类似tag的效果，背景透明， 3. 当鼠标进入时，在中间显示“click to enter full screen”， 类似tag的效果 4. 点击进入全屏 5, 增加快捷键操作，F 进入全屏， ESC 退出全屏，回到当前页面，Space或者鼠标点击，Toggle*/}
              {/* 颜色值显示在右上角 */}
              <Chip
                variant="bordered"
                className="absolute top-2 right-2 bg-transparent border-gray-500 text-gray-700 dark:text-gray-300 text-xs"
              >
                {colorValue}
              </Chip>

              {/* 鼠标悬停时显示提示 */}
              {isHovered && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <Chip
                    variant="bordered"
                    className="bg-transparent border-white text-white"
                  >
                    {t('Click to enter full screen')}
                  </Chip>
                </div>
              )}
            </div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <div className='w-full'>
          <Card>
            <CardBody>
              <CardHeader className='px-6'>
                <h3 className="text-lg font-semibold">{t('Color Selection')}</h3>
              </CardHeader>
            </CardBody>
          </Card>
          <Card className="mt-6">
            <CardHeader className='px-6'>
              <h3 className="text-lg font-semibold">{t('Download Image')}</h3>
            </CardHeader>
            <CardBody className='px-6'>
            </CardBody>
          </Card>
        </div>
      </div>
      <div className="grid lg:grid-cols-2 gap-8 mb-12">
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('Timer Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
        <Card className="w-full">
          <CardHeader className='px-6'>
            <h3 className="text-lg font-semibold">{t('OLED Protection Settings')}</h3>
          </CardHeader>
          <CardBody className='px-6'>
            <div className='aspect-video rounded-lg cursor-pointer border-2 border-gray-300 dark:border-gray-700 transition-all duration-300 hover:scale-[1.02] hover:border-black dark:hover:border-white relative overflow-hidden group'></div>
          </CardBody>
          <CardFooter></CardFooter>
        </Card>
      </div>
    </div >
  );
}
